// Simple test to verify the refactoring logic works
// This is a standalone test that doesn't require the full build system

// Mock classes for testing
data class EditRequest(
    val targetFile: String,
    val instructions: String,
    val codeEdit: String
)

sealed class ParseException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class InvalidFormatException(message: String, cause: Throwable? = null) : ParseException(message, cause)
    class MissingFieldException(fieldName: String, cause: Throwable? = null) : 
        ParseException("Required field '$fieldName' is missing", cause)
    class InvalidFieldException(fieldName: String, value: String, reason: String, cause: Throwable? = null) : 
        ParseException("Invalid value for field '$fieldName': '$value' - $reason", cause)
    class YamlParseException(message: String, cause: Throwable? = null) : ParseException(message, cause)
    class RegexParseException(message: String, cause: Throwable? = null) : ParseException(message, cause)
    class QuoteParseException(message: String, cause: Throwable? = null) : ParseException(message, cause)
}

// Simplified parser for testing (without YAML dependency)
class EditRequestParser {
    
    fun parse(content: String): EditRequest? {
        if (content.isBlank()) {
            return null
        }
        
        val errors = mutableListOf<ParseException>()
        
        // Try advanced format
        try {
            parseAsAdvancedFormat(content)?.let { return it }
        } catch (e: ParseException) {
            errors.add(e)
        }
        
        // Try legacy format
        try {
            parseAsLegacyFormat(content)?.let { return it }
        } catch (e: ParseException) {
            errors.add(e)
        }
        
        return null
    }
    
    fun parseAsAdvancedFormat(content: String): EditRequest? {
        return try {
            val targetFileRegex = """target_file\s*:\s*["']?([^"'\n]+)["']?""".toRegex()
            val instructionsRegex = """instructions\s*:\s*["']?([^"'\n]*?)["']?""".toRegex()
            val blockScalarPattern = """code_edit\s*:\s*\|\s*\n(.*?)(?=\n\S|\n*$)""".toRegex(RegexOption.DOT_MATCHES_ALL)
            val quotedStringPattern = """code_edit\s*:\s*["'](.*?)["']""".toRegex(RegexOption.DOT_MATCHES_ALL)

            val targetFileMatch = targetFileRegex.find(content)
                ?: throw ParseException.MissingFieldException("target_file")
            
            val instructionsMatch = instructionsRegex.find(content)
            val codeEditMatch = blockScalarPattern.find(content) ?: quotedStringPattern.find(content)
                ?: throw ParseException.MissingFieldException("code_edit")

            val codeEditContent = if (blockScalarPattern.matches(codeEditMatch.value)) {
                codeEditMatch.groupValues[1].trimEnd()
            } else {
                processEscapeSequences(codeEditMatch.groupValues[1])
            }

            val targetFile = targetFileMatch.groupValues[1].trim()
            val instructions = instructionsMatch?.groupValues?.get(1)?.trim() ?: ""
            
            validateEditRequest(targetFile, codeEditContent)

            EditRequest(
                targetFile = targetFile,
                instructions = instructions,
                codeEdit = codeEditContent
            )
        } catch (e: ParseException) {
            throw e
        } catch (e: Exception) {
            throw ParseException.RegexParseException("Failed to parse advanced format: ${e.message}", e)
        }
    }
    
    fun parseAsLegacyFormat(content: String): EditRequest? {
        return try {
            val targetFileRegex = """target_file["\s]*[:=]["\s]*["']([^"']+)["']""".toRegex()
            val instructionsRegex = """instructions["\s]*[:=]["\s]*["']([^"']*?)["']""".toRegex(RegexOption.DOT_MATCHES_ALL)

            val targetFileMatch = targetFileRegex.find(content)
                ?: throw ParseException.MissingFieldException("target_file")
            
            val instructionsMatch = instructionsRegex.find(content)
            val codeEditContent = extractCodeEditContent(content)
                ?: throw ParseException.MissingFieldException("code_edit")

            val targetFile = targetFileMatch.groupValues[1]
            val instructions = instructionsMatch?.groupValues?.get(1) ?: ""
            
            validateEditRequest(targetFile, codeEditContent)

            EditRequest(
                targetFile = targetFile,
                instructions = instructions,
                codeEdit = codeEditContent
            )
        } catch (e: ParseException) {
            throw e
        } catch (e: Exception) {
            throw ParseException.RegexParseException("Failed to parse legacy format: ${e.message}", e)
        }
    }
    
    private fun extractCodeEditContent(content: String): String? {
        return try {
            val codeEditStart = """code_edit["\s]*[:=]["\s]*["']""".toRegex().find(content) ?: return null
            val startIndex = codeEditStart.range.last + 1

            if (startIndex >= content.length) return null

            val openingQuote = content[startIndex - 1]
            var index = startIndex
            var escapeNext = false

            while (index < content.length) {
                val char = content[index]

                if (escapeNext) {
                    escapeNext = false
                } else if (char == '\\') {
                    escapeNext = true
                } else if (char == openingQuote) {
                    val extractedContent = content.substring(startIndex, index)
                    return processEscapeSequences(extractedContent)
                }

                index++
            }

            throw ParseException.QuoteParseException("Unclosed quote in code_edit field")
        } catch (e: ParseException) {
            throw e
        } catch (e: Exception) {
            throw ParseException.QuoteParseException("Failed to extract code_edit content: ${e.message}", e)
        }
    }
    
    private fun processEscapeSequences(content: String): String {
        return content
            .replace("\\n", "\n")
            .replace("\\\"", "\"")
            .replace("\\'", "'")
            .replace("\\\\", "\\")
    }
    
    private fun validateEditRequest(targetFile: String, codeEdit: String) {
        if (targetFile.isBlank()) {
            throw ParseException.InvalidFieldException("target_file", targetFile, "cannot be blank")
        }
        
        if (codeEdit.isBlank()) {
            throw ParseException.InvalidFieldException("code_edit", codeEdit, "cannot be blank")
        }
        
        if (targetFile.contains("..")) {
            throw ParseException.InvalidFieldException("target_file", targetFile, "path traversal not allowed")
        }
    }
}

// Test cases - run directly in script
run {
    val parser = EditRequestParser()
    
    println("Testing EditRequestParser refactoring...")
    
    // Test 1: Advanced format with block scalar
    val test1 = """
        target_file: src/Example.kt
        instructions: Add method
        code_edit: |
          class Example {
              fun method() {}
          }
    """.trimIndent()
    
    val result1 = parser.parse(test1)
    println("Test 1 - Advanced format: ${if (result1 != null) "PASS" else "FAIL"}")
    if (result1 != null) {
        println("  Target: ${result1.targetFile}")
        println("  Instructions: ${result1.instructions}")
        println("  Code contains 'class Example': ${result1.codeEdit.contains("class Example")}")
    }
    
    // Test 2: Advanced format with quoted string
    val test2 = """
        target_file: "test.kt"
        instructions: "Test instruction"
        code_edit: "fun test() {\n    println(\"Hello\")\n}"
    """.trimIndent()
    
    val result2 = parser.parse(test2)
    println("Test 2 - Advanced format with quotes: ${if (result2 != null) "PASS" else "FAIL"}")
    if (result2 != null) {
        println("  Code contains newlines: ${result2.codeEdit.contains("\n")}")
        println("  Code contains quotes: ${result2.codeEdit.contains("\"Hello\"")}")
    }
    
    // Test 3: Legacy format
    val test3 = """
        target_file = "legacy.kt"
        instructions = "Legacy instruction"
        code_edit = "class Legacy {}"
    """.trimIndent()
    
    val result3 = parser.parse(test3)
    println("Test 3 - Legacy format: ${if (result3 != null) "PASS" else "FAIL"}")
    
    // Test 4: Invalid format (should return null)
    val test4 = "This is not a valid format at all"
    val result4 = parser.parse(test4)
    println("Test 4 - Invalid format: ${if (result4 == null) "PASS" else "FAIL"}")
    
    // Test 5: Exception handling
    try {
        parser.parseAsAdvancedFormat("target_file: test.kt")  // Missing code_edit
        println("Test 5 - Exception handling: FAIL (should have thrown exception)")
    } catch (e: ParseException.MissingFieldException) {
        println("Test 5 - Exception handling: PASS (caught MissingFieldException)")
    } catch (e: Exception) {
        println("Test 5 - Exception handling: FAIL (wrong exception type: ${e::class.simpleName})")
    }
    
    println("Refactoring test completed!")
}
